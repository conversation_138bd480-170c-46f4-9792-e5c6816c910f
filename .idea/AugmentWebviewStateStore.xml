<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>