package handlers

import (
	"BackEnd/models"
	"BackEnd/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CallHandler struct {
	agoraService        *services.AgoraService
	notificationService *services.NotificationService
}

func NewCallHandler(agoraService *services.AgoraService, notificationService *services.NotificationService) *CallHandler {
	return &CallHandler{
		agoraService:        agoraService,
		notificationService: notificationService,
	}
}

// InitiateCall handles the call initiation request from User A
func (ch *CallHandler) InitiateCall(c *gin.Context) {
	var req models.CallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate that caller and callee are different
	if req.CallerID == req.CalleeID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "caller and callee cannot be the same"})
		return
	}

	// Create call session with Agora tokens
	session, err := ch.agoraService.CreateCallSession(req.CallerID, req.CalleeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create call session"})
		return
	}

	// Send notification to callee (mock)
	notificationReq := &models.NotificationRequest{
		UserID:      req.CalleeID,
		CallID:      session.CallID,
		CallerID:    req.CallerID,
		ChannelName: session.ChannelName,
		Token:       session.CalleeToken,
	}

	if err := ch.notificationService.SendCallNotification(notificationReq); err != nil {
		// Log error but don't fail the request
		// In production, you might want to handle this differently
		c.Header("X-Notification-Status", "failed")
	}

	// Return response to caller
	response := &models.CallResponse{
		CallID:      session.CallID,
		ChannelName: session.ChannelName,
		CallerToken: session.CallerToken,
		CalleeToken: session.CalleeToken, // Include for testing purposes
		AppID:       ch.agoraService.GetAppID(),
	}

	c.JSON(http.StatusOK, response)
}

// GetCallInfo returns call information (useful for testing)
func (ch *CallHandler) GetCallInfo(c *gin.Context) {
	callID := c.Param("call_id")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "call_id is required"})
		return
	}

	// In a real implementation, you would fetch this from a database
	// For now, return a simple response
	c.JSON(http.StatusOK, gin.H{
		"call_id": callID,
		"status":  "active",
		"message": "This is a mock response. In production, fetch from database.",
	})
}
