package services

import (
	"BackEnd/config"
	"BackEnd/models"
	"fmt"
	"time"

	rtctokenbuilder2 "github.com/AgoraIO/Tools/DynamicKey/AgoraDynamicKey/go/src/rtctokenbuilder2"
	"github.com/google/uuid"
)

type AgoraService struct {
	config *config.Config
}

func NewAgoraService(cfg *config.Config) *AgoraService {
	return &AgoraService{
		config: cfg,
	}
}

func (as *AgoraService) CreateCallSession(callerID, calleeID string) (*models.CallSession, error) {
	// Generate unique call ID and channel name
	callID := uuid.New().String()
	channelName := fmt.Sprintf("call_%s", callID)

	// Token expiration time (1 hour from now)
	expiration := uint32(time.Now().Add(time.Hour).Unix())

	// Generate token for caller (publisher role)
	callerToken, err := as.generateToken(channelName, callerID, rtctokenbuilder2.RolePublisher, expiration)
	if err != nil {
		return nil, fmt.Errorf("failed to generate caller token: %w", err)
	}

	// Generate token for callee (publisher role)
	calleeToken, err := as.generateToken(channelName, calleeID, rtctokenbuilder2.RolePublisher, expiration)
	if err != nil {
		return nil, fmt.Errorf("failed to generate callee token: %w", err)
	}

	session := &models.CallSession{
		CallID:      callID,
		CallerID:    callerID,
		CalleeID:    calleeID,
		ChannelName: channelName,
		CallerToken: callerToken,
		CalleeToken: calleeToken,
		CreatedAt:   time.Now().Unix(),
	}

	return session, nil
}

func (as *AgoraService) generateToken(channelName, uid string, agoraRole rtctokenbuilder2.Role, expiration uint32) (string, error) {
	token, err := rtctokenbuilder2.BuildTokenWithUid(
		as.config.Agora.AppID,
		as.config.Agora.AppCertificate,
		channelName,
		uid,
		agoraRole,
		expiration,
		expiration, // Privilege expired timestamp (same as token expiration)
	)
	if err != nil {
		return "", err
	}
	return token, nil
}

func (as *AgoraService) GetAppID() string {
	return as.config.Agora.AppID
}
