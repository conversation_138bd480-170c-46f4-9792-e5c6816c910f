package config

import (
	"os"
)

type Config struct {
	Server ServerConfig `json:"server"`
	Agora  AgoraConfig  `json:"agora"`
}

type ServerConfig struct {
	Port string `json:"port"`
}

type AgoraConfig struct {
	AppID          string `json:"app_id"`
	AppCertificate string `json:"app_certificate"`
}

func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8080"),
		},
		Agora: AgoraConfig{
			AppID:          getEnv("AGORA_APP_ID", "your_agora_app_id"),
			AppCertificate: getEnv("AGORA_APP_CERTIFICATE", "your_agora_app_certificate"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
