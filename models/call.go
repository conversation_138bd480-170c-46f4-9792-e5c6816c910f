package models

type CallRequest struct {
	CallerID string `json:"caller_id" binding:"required"`
	CalleeID string `json:"callee_id" binding:"required"`
}

type CallResponse struct {
	CallID      string `json:"call_id"`
	ChannelName string `json:"channel_name"`
	CallerToken string `json:"caller_token"`
	CalleeToken string `json:"callee_token"`
	AppID       string `json:"app_id"`
}

type CallSession struct {
	CallID      string `json:"call_id"`
	CallerID    string `json:"caller_id"`
	CalleeID    string `json:"callee_id"`
	ChannelName string `json:"channel_name"`
	CallerToken string `json:"caller_token"`
	CalleeToken string `json:"callee_token"`
	CreatedAt   int64  `json:"created_at"`
}

type NotificationRequest struct {
	UserID      string `json:"user_id"`
	CallID      string `json:"call_id"`
	CallerID    string `json:"caller_id"`
	ChannelName string `json:"channel_name"`
	Token       string `json:"token"`
}
