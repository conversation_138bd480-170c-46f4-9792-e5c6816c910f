package main

import (
	"BackEnd/config"
	"BackEnd/handlers"
	"BackEnd/services"
	"log"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize services
	agoraService := services.NewAgoraService(cfg)
	notificationService := services.NewNotificationService()

	// Initialize handlers
	callHandler := handlers.NewCallHandler(agoraService, notificationService)

	// Setup Gin router
	router := gin.Default()

	// Add CORS middleware for development
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{"status": "ok", "service": "call-service"})
	})

	// Call endpoints
	v1 := router.Group("/api/v1")
	{
		v1.POST("/call/initiate", callHandler.InitiateCall)
		v1.GET("/call/:call_id", callHandler.GetCallInfo)
	}

	// Start server
	port := ":" + cfg.Server.Port
	log.Printf("🚀 Call Service starting on port %s", cfg.Server.Port)
	log.Printf("📋 Available endpoints:")
	log.Printf("   GET  /health")
	log.Printf("   POST /api/v1/call/initiate")
	log.Printf("   GET  /api/v1/call/:call_id")
	log.Printf("💡 Make sure to set AGORA_APP_ID and AGORA_APP_CERTIFICATE environment variables")

	if err := router.Run(port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
